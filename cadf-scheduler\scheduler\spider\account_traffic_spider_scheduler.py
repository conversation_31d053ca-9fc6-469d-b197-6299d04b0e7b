from common_config.common_config import RedisKeyConfig
from models.models import Account
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    account_ids = [str(account.id) for account in await Account.find(Account.status == '在线').to_list()]
    num_added = await publish_messages_to_redis_set(RedisKeyConfig.XHS_ACCOUNT_TRAFFIC_METRICS_SPIDER_SET,
                                                    [{"id_": account_id} for account_id in account_ids])
    olog.info(f"成功添加 {num_added} 个账号ID，共找到 {len(account_ids)} 个在线账号")


# 调度频率：每2小时执行一次
@register_scheduler(trigger='cron', hour='*/2', minute='0')
class AccountTrafficSpiderScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        账号流量数据爬取,同时他也验证账号的在线状态
        """
        olog.info("开始执行在线账号爬取任务")
        await execute_task()
        olog.info("在线账号爬取任务执行完毕")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
