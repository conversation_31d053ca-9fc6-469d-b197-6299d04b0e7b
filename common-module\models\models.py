from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from beanie import Document
from pydantic import Field, BaseModel


# 支持的平台枚举
class SupportedPlatform(str, Enum):
    XHS = "小红书"
    DOUYIN = "抖音"
    WECHAT = "公众号"
    TOUTIAO = "今日头条"
    ZHIHU = "知乎"


# 图片信息 (用于嵌入到产品中)
class ImageInfo(BaseModel):
    oss_key: str
    order: int
    signed_url: Optional[str] = Field(default=None, title="图片签名URL")


# 数据字典 / 配置项
class DataDictionary(Document):
    category: Optional[str] = Field(default=None, title="字典类别")
    key: Optional[str] = Field(default=None, title="字典键")
    value: Optional[Any] = Field(default=None, title="字典值")

    class Settings:
        name = "data_dictionary"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======用户与角色=======

# 用户
class User(Document):
    username: Optional[str] = Field(default=None, title="用户名")
    password: Optional[str] = Field(default=None, title="密码")
    # 用户角色列表.
    # 可选值:
    # - advertiser(广告主)
    # - creator(流量主)
    # - captain(舰长)
    # - crew(舰员)
    # - admin(超级管理员)
    # 默认为 ['admin']
    roles: Optional[
        List[
            Literal[
                "user",
                "creator",
                "captain",
                "advertiser",
                "admin",
            ]
        ]
    ] = Field(default=None, title="用户角色列表")
    crew_invite_code: Optional[str] = Field(default=None, title="舰员邀请码")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")

    class Settings:
        name = "user"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 平台账号
class Account(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    name: Optional[str] = Field(default=None, title="账号名称")
    platform: Optional[SupportedPlatform] = Field(
        default=None, title="平台类型"
    )
    domain: Optional[str] = Field(default=None, title="账号领域")
    cookie: Optional[List[Dict[str, Any]]] = Field(default=None, title="存储原始 Cookie 对象列表")
    status: Optional[Literal["在线", "离线", "封禁"]] = Field(default=None, title="账号状态 (中文)")
    last_login_check_at: Optional[int] = Field(default=None, title="最后一次登录检测时间")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")

    class Settings:
        name = "account"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 舰员管理
class CrewManagement(Document):
    captain_user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(舰长)")
    crew_user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(舰员)")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")

    class Settings:
        name = "crew_management"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======产品与素材=======

# 产品
class Product(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    title: Optional[str] = Field(default=None, title="产品标题")
    description: Optional[str] = Field(default=None, title="产品描述")
    domain: Optional[List[str]] = Field(default=None, title="产品所属领域列表")
    images: Optional[List[ImageInfo]] = Field(default=None, title="产品图片列表")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    class Settings:
        name = "product"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# AI基础素材管理
class UserBasicMaterial(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    platform: Optional[SupportedPlatform] = Field(
        default=None, title="平台类型（如：小红书、抖音、微博等）"
    )
    domain: Optional[List[str]] = Field(default=None, title="素材所属领域列表")
    share_url: Optional[str] = Field(default=None, title="分享链接")
    title: Optional[str] = Field(default=None, title="素材标题")
    content: Optional[str] = Field(default=None, title="文本内容")
    images: Optional[List[ImageInfo]] = Field(default=None, title="图片列表")
    fetch_status: Optional[Literal["待爬取", "爬取中", "已完成", "失败"]] = Field(
        default=None, title="素材获取状态"
    )
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    class Settings:
        name = "user_basic_material"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 公共基础素材管理
class PublicBasicMaterial(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    platform: Optional[SupportedPlatform] = Field(
        default=None, title="平台类型"
    )
    domain: Optional[List[str]] = Field(default=None, title="素材所属领域列表")
    share_url: Optional[str] = Field(default=None, title="分享链接")
    title: Optional[str] = Field(default=None, title="素材标题")
    content: Optional[str] = Field(default=None, title="文本内容")
    images: Optional[List[ImageInfo]] = Field(default=None, title="图片列表")
    fetch_status: Optional[Literal["待爬取", "爬取中", "已完成", "失败"]] = Field(
        default=None, title="素材获取状态"
    )
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    class Settings:
        name = "public_basic_material"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======AI生成模块=======


# AI生成任务
class AiGenerationTask(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
    task_name: Optional[str] = Field(default=None, title="任务名称")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    class Settings:
        name = "ai_generation_task"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# AI生成素材
class AiGeneratedMaterial(Document):
    # 关联关系
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    ai_generation_task_id: Optional[str] = Field(default=None, title="关联的 AiGenerationTask 的ID")
    product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
    ai_basic_material_id: Optional[str] = Field(default=None, title="关联的 UserBasicMaterial 的ID")

    # 内容
    title: Optional[str] = Field(default=None, title="素材标题")
    content: Optional[str] = Field(default=None, title="文本内容")
    images: Optional[List[ImageInfo]] = Field(default=None, title="图片列表")
    image_generation_status: Optional[Literal["待生成", "生成中", "已完成", "失败"]] = Field(
        default=None, title="图片生成状态"
    )
    text_generation_status: Optional[Literal["待生成", "生成中", "已完成", "失败"]] = Field(
        default=None, title="文本生成状态"
    )
    generation_fail_reason: Optional[str] = Field(default=None, title="生成失败原因")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    class Settings:
        name = "ai_generated_material"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======任务=======


# 产品推广任务
class PromotionTask(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(广告主)")
    product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
    platform: Optional[SupportedPlatform] = Field(
        default=None, title="发布平台"
    )
    end_date: Optional[int] = Field(default=None, title="结束时间(时间戳)")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    class Settings:
        name = "promotion_task"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 用户接取的推广任务
class PromotionTaskDetail(Document):
    # 关联信息
    promotion_task_id: Optional[str] = Field(default=None, title="关联的 PromotionTask 的ID")
    ai_generated_material_id: Optional[str] = Field(default=None, title="关联的 AiGeneratedMaterial 的ID")

    # 用户与账号信息 (当 user_id 为 null 时表示任务素材可用待分配)
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(流量主) - Nullable")
    account_id: Optional[str] = Field(default=None, title="关联的 Account 的ID")
    accepted_at: Optional[int] = Field(default=None, title="任务接取时间(时间戳)")

    # 已发布作品信息
    publish_url: Optional[str] = Field(default=None, title="发布链接")
    publish_at: Optional[int] = Field(default=None, title="发布时间(时间戳)")

    # 验证状态
    validation_status: Optional[Literal["待验证", "成功", "失败"]] = Field(default=None, title="验证状态")
    # 验证信息
    validation_details: Optional[str] = Field(default=None, title="验证详情 (失败原因或错误信息)")

    class Settings:
        name = "promotion_task_detail"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======流量统计=======

# 账号流量指标
class AccountTrafficMetrics(Document):
    # 关联信息
    account_id: Optional[str] = Field(default=None, title="关联的 Account 的ID")
    product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
    ai_generated_material_id: Optional[str] = Field(default=None, title="关联的 AiGeneratedMaterial 的ID")
    promotion_task_detail_id: Optional[str] = Field(default=None, title="关联的 PromotionTaskDetail 的ID")
    # 其他信息
    platform: Optional[str] = Field(default=None, title="平台 (例如: '小红书', '抖音')")
    crawled_at: Optional[int] = Field(default=None, title="爬取时间戳")
    # 单个笔记的数据
    title: Optional[str] = Field(default=None, title="笔记/帖子标题")
    publish_time: Optional[int] = Field(default=None, title="笔记/帖子发布时间 (Unix 时间戳)")
    view_count: Optional[int] = Field(default=None, title="浏览量")
    like_count: Optional[int] = Field(default=None, title="点赞量")
    comment_count: Optional[int] = Field(default=None, title="评论量")
    favorite_count: Optional[int] = Field(default=None, title="收藏量")
    share_count: Optional[int] = Field(default=None, title="分享量")

    class Settings:
        name = "account_traffic_metrics"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data

# 账号流量指标快照
class AccountTrafficMetricsSnapshot(Document):
    # 关联信息
    account_id: Optional[str] = Field(default=None, title="关联的 Account 的ID")
    product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
    ai_generated_material_id: Optional[str] = Field(default=None, title="关联的 AiGeneratedMaterial 的ID")
    promotion_task_detail_id: Optional[str] = Field(default=None, title="关联的 PromotionTaskDetail 的ID")
    # 其他信息
    platform: Optional[str] = Field(default=None, title="平台 (例如: '小红书', '抖音')")
    crawled_at: Optional[int] = Field(default=None, title="爬取时间戳")
    snapshot_at: Optional[int] = Field(default=None, title="快照时间戳")
    # 单个笔记的数据
    title: Optional[str] = Field(default=None, title="笔记/帖子标题")
    publish_time: Optional[int] = Field(default=None, title="笔记/帖子发布时间 (Unix 时间戳)")
    view_count: Optional[int] = Field(default=None, title="浏览量")
    like_count: Optional[int] = Field(default=None, title="点赞量")
    comment_count: Optional[int] = Field(default=None, title="评论量")
    favorite_count: Optional[int] = Field(default=None, title="收藏量")
    share_count: Optional[int] = Field(default=None, title="分享量")

    class Settings:
        name = "account_traffic_metrics_snapshot"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 产品流量指标
class ProductTrafficMetrics(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(广告主)")
    product_id: Optional[str] = Field(default=None, title="关联的 Product 的ID")
    platform: Optional[str] = Field(default=None, title="任务平台列表")
    total_view_count: Optional[int] = Field(default=None, title="总浏览量")
    total_like_count: Optional[int] = Field(default=None, title="总点赞量")
    total_comment_count: Optional[int] = Field(default=None, title="总评论量")
    total_favorite_count: Optional[int] = Field(default=None, title="总收藏量")
    total_share_count: Optional[int] = Field(default=None, title="总分享量")
    last_updated_at: Optional[int] = Field(default=None, title="最后更新时间")

    class Settings:
        name = "product_traffic_metrics"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======收益模块=======

# 用户每日任务收益记录
class UserDailyRevenue(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID")
    promotion_task_detail_id: Optional[str] = Field(default=None, title="关联的 PromotionTaskDetail 的ID")
    date: Optional[int] = Field(default=None, title="记录的日期 (当天零点的时间戳)")
    daily_revenue: Optional[int] = Field(default=None, title="此任务在当日产生的收益金额")
    status: Optional[Literal["未结算", "已结算"]] = Field(default=None, title="收益状态 (未结算, 已结算)")
    settled_at: Optional[int] = Field(default=None, title="结算时间 (时间戳, 仅当 status 为 已结算 时有效)")
    created_at: Optional[int] = Field(default=None, title="记录创建时间")

    class Settings:
        name = "daily_task_revenue"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 舰长每日收益记录
class CaptainDailyRevenue(Document):
    captain_user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(舰长)")
    crew_user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(舰员)")
    date: Optional[int] = Field(default=None, title="出账日期 (当天零点的时间戳)")
    daily_revenue: Optional[int] = Field(default=None, title="舰长从该舰员本日获得的佣金金额 (单位: 元)")
    daily_task_revenue_ids: Optional[List[str]] = Field(default=None, title="关联的 DailyTaskRevenue 的ID列表")
    status: Optional[Literal["未结算", "已结算"]] = Field(default=None, title="结算状态")
    settled_at: Optional[int] = Field(default=None, title="结算时间(时间戳, 仅当 status 为 已结算 时有效)")

    class Settings:
        name = "captain_daily_revenue"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# =======消费模块=======
# 用户消费账户
class CostUserAccount(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID，唯一标识")
    balance: Optional[float] = Field(default=None, title="账户余额")
    updated_at: Optional[int] = Field(default=None, title="更新时间")

    class Settings:
        name = "cost_user_account"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 充值记录
class CostRechargeRecord(Document):
    cost_user_account_id: Optional[str] = Field(
        default=None, title="关联的 CostUserAccount 的ID"
    )
    amount: Optional[float] = Field(default=None, gt=0.0, title="充值金额")
    recharge_method: Optional[Literal["手工"]] = Field(default=None, title="充值方式，例如：支付宝、微信")
    status: Optional[Literal["待处理", "成功", "失败"]] = Field(default=None, title="充值状态：待处理，成功，失败")
    created_at: Optional[int] = Field(default=None, title="创建时间")
    updated_at: Optional[int] = Field(default=None, title="更新时间")

    class Settings:
        name = "cost_recharge_record"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data


# 消费记录
class CostConsumptionRecord(Document):
    cost_user_account_id: Optional[str] = Field(
        default=None, title="关联的 CostUserAccount 的ID"
    )
    project_name: Optional[Literal["图片生成", "文本生成", "流量结算"]] = Field(
        default=None, title="项目名称，来自config.py中的COST_PROJECTS配置"
    )
    amount: Optional[float] = Field(default=None, gt=0.0, title="消费金额")
    consumption_type: Optional[Literal["一次性"]] = Field(default=None, title="消费类型：一次性")
    description: Optional[str] = Field(default=None, title="消费描述")
    created_at: Optional[int] = Field(default=None, title="创建时间")

    class Settings:
        name = "cost_consumption_record"

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data
