from datetime import datetime, timedelta, time as dt_time

import pytz

from models.models import (
    PromotionTaskDetail,
    DailyTaskRevenue,
)
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler


async def execute_task() -> None:
    """
    处理流量主每日任务收益计算
    """
    shanghai_tz = pytz.timezone('Asia/Shanghai')
    now_shanghai = datetime.now(shanghai_tz)
    yesterday_shanghai = now_shanghai.date() - timedelta(days=1)
    start_dt_shanghai = shanghai_tz.localize(
        datetime.combine(yesterday_shanghai, dt_time.min)
    )
    end_dt_shanghai = shanghai_tz.localize(
        datetime.combine(yesterday_shanghai, dt_time.max)
    )
    start_of_yesterday_utc_ts = int(start_dt_shanghai.timestamp())
    end_of_yesterday_utc_ts = int(end_dt_shanghai.timestamp())
    olog.info(f"计算时间范围: {start_of_yesterday_utc_ts} - {end_of_yesterday_utc_ts}")

    pipeline = [
        {
            "$match": {
                "validation_status": "成功",
                "account_id": {"$ne": None},
                "ai_generated_material_id": {"$ne": None}
            }
        },
        {
            "$lookup": {
                "from": "ai_generated_materials",
                "localField": "ai_generated_material_id",
                "foreignField": "_id",
                "as": "material",
                "pipeline": [
                    {
                        "$match": {
                            "title": {"$ne": None, "$ne": ""}
                        }
                    },
                    {
                        "$project": {
                            "title": 1
                        }
                    }
                ]
            }
        },
        {
            "$match": {
                "material": {"$ne": []}
            }
        },
        {
            "$addFields": {
                "material_title": {
                    "$arrayElemAt": ["$material.title", 0]
                }
            }
        },
        {
            "$lookup": {
                "from": "account_metrics",
                "let": {
                    "account_id": "$account_id",
                    "title": "$material_title",
                    "publish_at": "$publish_at"
                },
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$account_id", "$$account_id"]},
                                    {"$eq": ["$title", "$$title"]},
                                    {"$lte": ["$crawled_at", end_of_yesterday_utc_ts]}
                                ]
                            }
                        }
                    },
                    {
                        "$sort": {"crawled_at": -1}
                    },
                    {
                        "$limit": 1
                    },
                    {
                        "$addFields": {
                            "is_valid_publish_time": {
                                "$cond": {
                                    "if": {
                                        "$and": [
                                            {"$ne": ["$$publish_at", None]},
                                            {"$ne": ["$publish_time", None]}
                                        ]
                                    },
                                    "then": {
                                        "$lte": [
                                            {
                                                "$abs": {
                                                    "$subtract": ["$$publish_at", "$publish_time"]
                                                }
                                            },
                                            86400
                                        ]
                                    },
                                    "else": True
                                }
                            }
                        }
                    }
                ],
                "as": "end_metrics"
            }
        },
        {
            "$lookup": {
                "from": "account_metrics",
                "let": {
                    "account_id": "$account_id",
                    "title": "$material_title",
                    "publish_at": "$publish_at"
                },
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$account_id", "$$account_id"]},
                                    {"$eq": ["$title", "$$title"]},
                                    {"$lt": ["$crawled_at", start_of_yesterday_utc_ts]}
                                ]
                            }
                        }
                    },
                    {
                        "$sort": {"crawled_at": -1}
                    },
                    {
                        "$limit": 1
                    },
                    {
                        "$addFields": {
                            "is_valid_publish_time": {
                                "$cond": {
                                    "if": {
                                        "$and": [
                                            {"$ne": ["$$publish_at", None]},
                                            {"$ne": ["$publish_time", None]}
                                        ]
                                    },
                                    "then": {
                                        "$lte": [
                                            {
                                                "$abs": {
                                                    "$subtract": ["$$publish_at", "$publish_time"]
                                                }
                                            },
                                            86400
                                        ]
                                    },
                                    "else": True
                                }
                            }
                        }
                    }
                ],
                "as": "start_metrics"
            }
        },
        {
            "$addFields": {
                "end_view_count": {
                    "$cond": {
                        "if": {
                            "$and": [
                                {"$gt": [{"$size": "$end_metrics"}, 0]},
                                {
                                    "$eq": [
                                        {"$arrayElemAt": ["$end_metrics.is_valid_publish_time", 0]},
                                        True
                                    ]
                                }
                            ]
                        },
                        "then": {
                            "$ifNull": [
                                {"$arrayElemAt": ["$end_metrics.view_count", 0]},
                                0
                            ]
                        },
                        "else": 0
                    }
                },
                "start_view_count": {
                    "$cond": {
                        "if": {
                            "$and": [
                                {"$gt": [{"$size": "$start_metrics"}, 0]},
                                {
                                    "$eq": [
                                        {"$arrayElemAt": ["$start_metrics.is_valid_publish_time", 0]},
                                        True
                                    ]
                                }
                            ]
                        },
                        "then": {
                            "$ifNull": [
                                {"$arrayElemAt": ["$start_metrics.view_count", 0]},
                                0
                            ]
                        },
                        "else": 0
                    }
                }
            }
        },
        {
            "$addFields": {
                "daily_view_increase": {
                    "$max": [
                        0,
                        {"$subtract": ["$end_view_count", "$start_view_count"]}
                    ]
                }
            }
        },
        {
            "$addFields": {
                "daily_revenue_cents": {
                    "$floor": {
                        "$divide": ["$daily_view_increase", 100]
                    }
                }
            }
        },
        {
            "$match": {
                "daily_view_increase": {"$gt": 0}
            }
        },
        {
            "$project": {
                "user_id": 1,
                "task_id": {"$toString": "$_id"},
                "daily_view_increase": 1,
                "daily_revenue_cents": 1
            }
        }
    ]
    task_metrics = await PromotionTaskDetail.aggregate(pipeline).to_list()
    olog.info(f"通过聚合管道获取到 {len(task_metrics)} 个任务指标")

    for metric in task_metrics:
        await DailyTaskRevenue.find_one({
            "user_id": metric["user_id"],
            "promotion_task_detail_id": metric["task_id"],
            "date": start_of_yesterday_utc_ts
        }).upsert(
            {
                "$set": {
                    "daily_views": metric["daily_view_increase"],
                    "daily_revenue": metric["daily_revenue_cents"]
                }
            },
            on_insert={
                "user_id": metric["user_id"],
                "promotion_task_detail_id": metric["task_id"],
                "date": start_of_yesterday_utc_ts
            }
        )

    olog.info(f"处理完成，共处理 {len(task_metrics)} 个任务，产生收益 {len(task_metrics)} 个")


# @register_scheduler(trigger="cron", hour="7", minute="0")
class DailyTaskRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行每日任务收益计算任务
        
        调度频率：每天早上7点执行一次
        """
        olog.info("开始执行每日任务收益计算任务")
        await execute_task()
        olog.info("每日任务收益计算任务执行完毕")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
