import datetime

from models.models import Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrewManagement
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler


async def execute_task() -> None:
    """
    处理舰长每日收益计算
    """
    today_timestamp = int(datetime.datetime.combine(
        datetime.date.today(),
        datetime.time.min
    ).timestamp())

    pipeline = [
        {
            "$lookup": {
                "from": "daily_task_revenue",
                "localField": "crew_user_id",
                "foreignField": "user_id",
                "as": "crew_revenues"
            }
        },
        {
            "$match": {
                "crew_revenues.date": today_timestamp
            }
        },
        {
            "$group": {
                "_id": "$captain_user_id",
                "crew_data": {
                    "$push": {
                        "crew_id": "$crew_user_id",
                        "revenues": {
                            "$filter": {
                                "input": "$crew_revenues",
                                "cond": {
                                    "$eq": ["$$this.date", today_timestamp]
                                }
                            }
                        }
                    }
                }
            }
        },
        {
            "$project": {
                "captain_id": "$_id",
                "crew_data": {
                    "$filter": {
                        "input": "$crew_data",
                        "cond": {
                            "$gt": [{"$size": "$$this.revenues"}, 0]
                        }
                    }
                }
            }
        }
    ]

    async for captain_data in CrewManagement.aggregate(pipeline):
        captain_id = captain_data["captain_id"]
        total_captain_revenue_today = 0
        olog.info(f"开始计算舰长 {captain_id} 的收益...")

        for crew_info in captain_data["crew_data"]:
            crew_id = crew_info["crew_id"]
            revenues = crew_info["revenues"]
            crew_total_revenue_today = sum(r["daily_revenue"] for r in revenues)
            captain_commission = int(crew_total_revenue_today * 0.3)

            if captain_commission > 0:
                processed_revenue_ids = [str(r["_id"]) for r in revenues]
                await CaptainDailyRevenue.find_one(
                    (CaptainDailyRevenue.captain_user_id == captain_id) &
                    (CaptainDailyRevenue.crew_user_id == crew_id) &
                    (CaptainDailyRevenue.date == today_timestamp)
                ).upsert(
                    {
                        "$set": {
                            "daily_revenue": captain_commission,
                            "daily_task_revenue_ids": processed_revenue_ids,
                            "status": "未结算",
                            "settled_at": None
                        }
                    },
                    on_insert={
                        "captain_user_id": captain_id,
                        "crew_user_id": crew_id,
                        "date": today_timestamp
                    }
                )
                olog.info(f"为舰长 {captain_id} 从舰员 {crew_id} 创建收益记录，金额: {captain_commission}")
                total_captain_revenue_today += captain_commission

    olog.info(f"舰长 {captain_id} 当日总计获得佣金: {total_captain_revenue_today}")


# @register_scheduler(trigger="cron", hour="7", minute="0")
class CaptainDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行舰长每日收益计算任务
        
        调度频率：每天早上7点执行一次
        """
        olog.info("开始计算舰长每日收益...")
        await execute_task()
        olog.info("舰长每日收益计算完成。")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
